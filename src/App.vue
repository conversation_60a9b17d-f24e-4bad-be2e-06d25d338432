<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const videoTransform = ref('translateY(0px)')
let ticking = false

const updateParallax = () => {
  const scrolled = window.pageYOffset
  const videoSection = document.querySelector('.video-parallax-section') as HTMLElement

  if (videoSection) {
    const rect = videoSection.getBoundingClientRect()
    const sectionTop = scrolled + rect.top
    const sectionHeight = rect.height
    const windowHeight = window.innerHeight

    // Calculate when the section is in view
    const sectionBottom = sectionTop + sectionHeight
    const viewportTop = scrolled
    const viewportBottom = scrolled + windowHeight

    // Only apply parallax when section is in viewport
    if (sectionBottom >= viewportTop && sectionTop <= viewportBottom) {
      // Calculate how much of the section is visible and scroll progress
      const scrollStart = sectionTop - windowHeight
      const scrollEnd = sectionBottom
      const scrollRange = scrollEnd - scrollStart
      const scrollProgress = Math.max(0, Math.min(1, (scrolled - scrollStart) / scrollRange))

      // Video is 130% height, so we have 30% extra (15% on each side)
      // Map scroll progress to video movement range
      const maxMovement = sectionHeight * 0.15 // 15% of section height
      const yPos = -maxMovement + (scrollProgress * maxMovement * 2)

      videoTransform.value = `translateY(${yPos}px)`
    }
  }

  ticking = false
}

const onScroll = () => {
  if (!ticking) {
    requestAnimationFrame(updateParallax)
    ticking = true
  }
}

onMounted(() => {
  window.addEventListener('scroll', onScroll, { passive: true })
  updateParallax() // Initial call
})

onUnmounted(() => {
  window.removeEventListener('scroll', onScroll)
})
</script>

<template>
  <!--Header -->
  <header>
    <nav class="fixed left-4 top-4 right-4 z-99 gap-2 flex justify-between items-center">

      <!-- Logo -->
      <div class="flex items-center justify-center flex-initial">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 39" class="w-16 h-auto">
          <path fill="#000" d="M78.787 17.78c.422.122.633.404.633.843v2.162c0 .44-.211.72-.633.844-1.6.264-2.549 1.125-2.549 2.32 0 .633.053 1.863.14 3.71.106 1.827.16 3.11.16 3.831 0 2.268-.669 4.043-2.022 5.291-1.336 1.248-3.182 1.846-5.538 1.793-.369 0-.58-.14-.597-.422l-.352-3.093c-.035-.457.194-.686.668-.686 1.846 0 2.971-1.125 2.971-2.953 0-.37-.053-1.6-.158-3.727-.106-2.127-.158-3.603-.158-4.447 0-1.705.826-2.9 2.496-3.55-1.67-.651-2.496-1.829-2.496-3.534 0-.844.052-2.32.158-4.447.105-2.127.158-3.358.158-3.727 0-1.828-1.125-2.953-2.97-2.953-.475 0-.704-.228-.669-.685l.352-3.094c.017-.281.228-.422.597-.422 2.356-.053 4.202.545 5.538 1.793 1.353 1.248 2.021 3.023 2.021 5.291 0 .72-.053 2.004-.158 3.85-.088 1.828-.14 3.058-.14 3.691 0 1.195.948 2.057 2.548 2.32Zm-18.844 6.908v-3.586c0-.616.457-1.073 1.073-1.073h5.59c.58 0 1.054.492 1.054 1.073v8.35c0 .421-.246.896-.58 1.089-.967.58-2.25 1.055-3.85 1.459-1.6.404-3.093.598-4.464.598-2.496 0-4.782-.598-6.856-1.776a13.345 13.345 0 0 1-4.922-4.869c-1.195-2.056-1.793-4.306-1.793-6.767 0-2.461.598-4.711 1.793-6.768a13.25 13.25 0 0 1 4.905-4.852c2.074-1.177 4.359-1.775 6.855-1.775 1.283 0 2.672.229 4.13.703 1.46.457 2.62 1.02 3.481 1.705.282.229.44.545.44.88 0 .192-.053.386-.176.58l-3.2 4.798a.899.899 0 0 1-.773.44 1.05 1.05 0 0 1-.685-.264c-.37-.317-.88-.58-1.494-.791-.615-.229-1.178-.334-1.705-.334-3.182-.053-5.889 2.566-5.819 5.678-.07 3.11 2.637 5.73 5.819 5.677.228 0 .773-.088 1.177-.175ZM42.98 32h-5.818c-.685 0-1.002-.316-1.072-1.037l-1.248-11.531-2.479 11.724c-.21.791-.492 1.055-1.177 1.055h-5.819c-.65 0-.949-.229-1.16-1.055l-2.479-11.69-1.248 11.497c-.07.72-.404 1.037-1.09 1.037h-5.8c-.756 0-1.195-.37-1.108-1.09l3.006-23.537c.088-.703.457-1.055 1.09-1.055h7.436c.685 0 1.09.352 1.248 1.037l3.023 13.834 3.006-13.834c.158-.685.58-1.037 1.266-1.037h7.435c.633 0 .985.352 1.073 1.055L44.07 30.91c.088.72-.334 1.09-1.09 1.09Zm-31.517 2.373c.475 0 .703.229.668.686l-.352 3.093c-.017.282-.228.422-.597.422-2.356.053-4.202-.545-5.555-1.793-1.336-1.248-2.004-3.023-2.004-5.29 0-.721.053-2.005.14-3.833.106-1.846.159-3.076.159-3.709 0-1.195-.95-2.056-2.549-2.32-.422-.123-.633-.404-.633-.844v-2.162c0-.44.211-.72.633-.844 1.6-.263 2.549-1.125 2.549-2.32 0-.633-.053-1.863-.158-3.691-.088-1.846-.14-3.13-.14-3.85-.001-2.268.667-4.043 2.003-5.291C6.98 1.379 8.827.781 11.182.834c.369 0 .58.14.597.422l.352 3.094c.035.457-.194.685-.668.685-1.846 0-2.97 1.125-2.97 2.953 0 .37.052 1.6.157 3.727.106 2.127.159 3.603.159 4.447 0 1.705-.827 2.883-2.496 3.533 1.67.65 2.496 1.846 2.496 3.551 0 .844-.053 2.32-.159 4.447-.105 2.127-.158 3.358-.158 3.727 0 1.828 1.125 2.953 2.97 2.953Z"/>
        </svg>
      </div>

      <!-- Hamburger -->
<!--      <div class="flex items-center flex-grow justify-end">-->
<!--        <button class="flex flex-col justify-between w-8 h-5 focus:outline-none">-->
<!--          <span class="w-full h-0.5 bg-gray-800 rounded-sm"></span>-->
<!--          <span class="w-full h-0.5 bg-gray-800 rounded-sm"></span>-->
<!--          <span class="w-full h-0.5 bg-gray-800 rounded-sm"></span>-->
<!--        </button>-->
<!--      </div>-->
    </nav>
  </header>

  <!-- Main -->
  <main class="w-screen min-h-screen p-4">

    <section class="max-w-6xl mx-auto min-h-screen flex flex-col gap-8 justify-end py-40">
      <h1 class="text-7xl text-shadow-slate-900 max-w-6xl coco">
          I build custom digital systems that actually <span class="text-slate-600 font-bold">solve</span> business problems.
      </h1>
      <p class="text-xl font-light text-gray-600 max-w-4xl">
        From broken booking flows to legacy integrations, I help hospitality and experience-led businesses
        streamline operations, simplify tech, and deliver better digital experiences
        — without the agency overhead.
      </p>
      <div class="flex flex-row">
        <a href="https://calendly.com/hello-martingreenwood/30min" target="_blank" class="group relative inline-flex h-[calc(48px+8px)] items-center justify-center rounded-full bg-slate-700 py-1 pl-6 pr-14 font-medium text-neutral-50"><span class="z-10 pr-2">Book a Call</span><div class="absolute right-1 inline-flex h-12 w-12 items-center justify-end rounded-full bg-slate-500 transition-[width] group-hover:w-[calc(100%-8px)]"><div class="mr-3.5 flex items-center justify-center"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-50"><path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg></div></div></a>
      </div>
    </section>
  </main>

  <section class="video-parallax-section bg-stone-100 h-[75vh] overflow-hidden relative">
    <video
      autoplay
      loop
      muted
      playsinline
      class="absolute w-full h-[140%] object-cover"
      style="top: -20%; left: 0;"
      :style="{ transform: videoTransform }"
    >
      <source src="./assets/video/video.mp4" type="video/mp4">
      Your browser does not support the video tag.
    </video>
  </section>

  <!-- services -->
  <section class="py-34">
    <div class="flex flex-col gap-8 max-w-6xl mx-auto">
      <p class="text-xl font-light text-gray-600 max-w-2xl">
        I design, build, and integrate digital systems for guest-centric, experience-driven, and operationally complex businesses. Whether it’s customer-facing tools or internal dashboards, I deliver clean, scalable solutions that make life easier for teams and their users.
      </p>

      <div class="flex flex-col md:flex-row gap-8">
        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Systems Integration
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Connect your booking engine, CRM, ticketing, POS, signage, and CMS into one reliable, futureproof stack.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>API integrations</li>
            <li>CRM/PMS workflows</li>
            <li>Spektrix, hospitality, and legacy system support</li>
          </ul>
        </div>

        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Data Dashboards &amp; Visualisation
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Live dashboards, custom reports, and conversion tracking — built for real-world decision-making, not just data dumps.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>Guest behaviour insights</li>
            <li>Marketing &amp; sales metrics</li>
            <li>Ops dashboards & group performance tracking</li>
          </ul>
        </div>

        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Digital Signage &amp; On-Site Displays
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Custom signage systems that pull from live data — built for theatres, hotels, events, and dynamic venue screens.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>Foyer &amp; event signage</li>
            <li>Wayfinding &amp; guest comms</li>
            <li>CMS-linked display tech</li>
          </ul>
        </div>

        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Web &amp; App Development
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Modern Laravel and Vue apps, powerful CMS builds, and fast, accessible frontends — all tailored to your users.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>Vue &amp; Laravel custom builds</li>
            <li>Statamic &amp; WordPress development</li>
            <li>Guest portals, booking flows, staff apps</li>
          </ul>
        </div>

        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Booking Engines &amp; Ticketing
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Integrated booking flows that play nicely with your existing systems, including full Spektrix support for venues.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>Custom booking interfaces</li>
            <li>Real-time availability & logic</li>
            <li>End-to-end ticketing integrations</li>
          </ul>
        </div>

        <div class="card">
          <img src="https://placeimg.com/1000/1000/arch" alt="">
          <div class="flex items-center gap-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-6" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48" d="m268 112 144 144-144 144m124-144H100"/>
            </svg>
            <h3 class="text-xl text-shadow-slate-900 coco">
              Technical Strategy &amp; Consulting
            </h3>
          </div>
          <p class="block text-slate-600 leading-normal font-light">
            Not sure where to start? I help teams audit what’s broken, plan what’s next, and ship fast — without overengineering.
          </p>
          <ul class="text-slate-700 leading-relaxed">
            <li>Discovery &amp; technical audits</li>
            <li>Project rescue & legacy fixes</li>
            <li>Long-term dev partner, if you need one</li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA -->
  <section class="bg-slate-700 text-neutral-100 py-16">
    <div class="flex flex-col gap-8 items-center max-w-6xl mx-auto">
      <h2 class="text-5xl max-w-6xl coco">
        Not sure where your project fits?
      </h2>
      <p class="text-xl font-light  max-w-4xl">
        Get in touch for a chat.
      </p>
      <div class="flex flex-row">
        <a href="https://calendly.com/hello-martingreenwood/30min" target="_blank" class="group relative inline-flex h-[calc(48px+8px)] items-center justify-center rounded-full bg-slate-900 py-1 pl-6 pr-14 font-medium text-neutral-50"><span class="z-10 pr-2">Book a Call</span><div class="absolute right-1 inline-flex h-12 w-12 items-center justify-end rounded-full bg-slate-500 transition-[width] group-hover:w-[calc(100%-8px)]"><div class="mr-3.5 flex items-center justify-center"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-50"><path d="M8.14645 3.14645C8.34171 2.95118 8.65829 2.95118 8.85355 3.14645L12.8536 7.14645C13.0488 7.34171 13.0488 7.65829 12.8536 7.85355L8.85355 11.8536C8.65829 12.0488 8.34171 12.0488 8.14645 11.8536C7.95118 11.6583 7.95118 11.3417 8.14645 11.1464L11.2929 8H2.5C2.22386 8 2 7.77614 2 7.5C2 7.22386 2.22386 7 2.5 7H11.2929L8.14645 3.85355C7.95118 3.65829 7.95118 3.34171 8.14645 3.14645Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg></div></div></a>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="bg-gray-800 text-white py-8">
    <div class="max-w-6xl mx-auto px-4 md:px-8">
      <div class="flex flex-col md:flex-row justify-between">
        <div class="mb-6 md:mb-0 ">
          <h3 class="text-xl font-bold mb-2 uppercase coco flex flex-col gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 80 39" class="w-16 h-auto fill-neutral-100">
              <path fill="currentColor" d="M78.787 17.78c.422.122.633.404.633.843v2.162c0 .44-.211.72-.633.844-1.6.264-2.549 1.125-2.549 2.32 0 .633.053 1.863.14 3.71.106 1.827.16 3.11.16 3.831 0 2.268-.669 4.043-2.022 5.291-1.336 1.248-3.182 1.846-5.538 1.793-.369 0-.58-.14-.597-.422l-.352-3.093c-.035-.457.194-.686.668-.686 1.846 0 2.971-1.125 2.971-2.953 0-.37-.053-1.6-.158-3.727-.106-2.127-.158-3.603-.158-4.447 0-1.705.826-2.9 2.496-3.55-1.67-.651-2.496-1.829-2.496-3.534 0-.844.052-2.32.158-4.447.105-2.127.158-3.358.158-3.727 0-1.828-1.125-2.953-2.97-2.953-.475 0-.704-.228-.669-.685l.352-3.094c.017-.281.228-.422.597-.422 2.356-.053 4.202.545 5.538 1.793 1.353 1.248 2.021 3.023 2.021 5.291 0 .72-.053 2.004-.158 3.85-.088 1.828-.14 3.058-.14 3.691 0 1.195.948 2.057 2.548 2.32Zm-18.844 6.908v-3.586c0-.616.457-1.073 1.073-1.073h5.59c.58 0 1.054.492 1.054 1.073v8.35c0 .421-.246.896-.58 1.089-.967.58-2.25 1.055-3.85 1.459-1.6.404-3.093.598-4.464.598-2.496 0-4.782-.598-6.856-1.776a13.345 13.345 0 0 1-4.922-4.869c-1.195-2.056-1.793-4.306-1.793-6.767 0-2.461.598-4.711 1.793-6.768a13.25 13.25 0 0 1 4.905-4.852c2.074-1.177 4.359-1.775 6.855-1.775 1.283 0 2.672.229 4.13.703 1.46.457 2.62 1.02 3.481 1.705.282.229.44.545.44.88 0 .192-.053.386-.176.58l-3.2 4.798a.899.899 0 0 1-.773.44 1.05 1.05 0 0 1-.685-.264c-.37-.317-.88-.58-1.494-.791-.615-.229-1.178-.334-1.705-.334-3.182-.053-5.889 2.566-5.819 5.678-.07 3.11 2.637 5.73 5.819 5.677.228 0 .773-.088 1.177-.175ZM42.98 32h-5.818c-.685 0-1.002-.316-1.072-1.037l-1.248-11.531-2.479 11.724c-.21.791-.492 1.055-1.177 1.055h-5.819c-.65 0-.949-.229-1.16-1.055l-2.479-11.69-1.248 11.497c-.07.72-.404 1.037-1.09 1.037h-5.8c-.756 0-1.195-.37-1.108-1.09l3.006-23.537c.088-.703.457-1.055 1.09-1.055h7.436c.685 0 1.09.352 1.248 1.037l3.023 13.834 3.006-13.834c.158-.685.58-1.037 1.266-1.037h7.435c.633 0 .985.352 1.073 1.055L44.07 30.91c.088.72-.334 1.09-1.09 1.09Zm-31.517 2.373c.475 0 .703.229.668.686l-.352 3.093c-.017.282-.228.422-.597.422-2.356.053-4.202-.545-5.555-1.793-1.336-1.248-2.004-3.023-2.004-5.29 0-.721.053-2.005.14-3.833.106-1.846.159-3.076.159-3.709 0-1.195-.95-2.056-2.549-2.32-.422-.123-.633-.404-.633-.844v-2.162c0-.44.211-.72.633-.844 1.6-.263 2.549-1.125 2.549-2.32 0-.633-.053-1.863-.158-3.691-.088-1.846-.14-3.13-.14-3.85-.001-2.268.667-4.043 2.003-5.291C6.98 1.379 8.827.781 11.182.834c.369 0 .58.14.597.422l.352 3.094c.035.457-.194.685-.668.685-1.846 0-2.97 1.125-2.97 2.953 0 .37.052 1.6.157 3.727.106 2.127.159 3.603.159 4.447 0 1.705-.827 2.883-2.496 3.533 1.67.65 2.496 1.846 2.496 3.551 0 .844-.053 2.32-.159 4.447-.105 2.127-.158 3.358-.158 3.727 0 1.828 1.125 2.953 2.97 2.953Z"/>
            </svg>
            <span class="font-bold text-2xl uppercase">Martin Greenwood</span>
          </h3>
          <p class="text-neutral-400 text-sm">Web Consulting &amp; Development</p>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-8">
          <div>
            <h4 class="text-lg font-semibold mb-3">Links</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">About</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
            </ul>
          </div>
          <div>
            <h4 class="text-lg font-semibold mb-3">Legal</h4>
            <ul class="space-y-2">
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Privacy</a></li>
              <li><a href="#" class="text-gray-400 hover:text-white transition-colors">Terms</a></li>
            </ul>
          </div>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-8 pt-6 text-center text-gray-400 text-xs">
        <p>&copy; 2025 Neurospicy Studio Ltd. All rights reserved.</p>
      </div>
    </div>
  </footer>
</template>
